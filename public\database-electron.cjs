const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { app } = require('electron');

let db = null;

// تحديد مسار قاعدة البيانات
const getDBPath = () => {
  try {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'furniture_factory.db');
  } catch (error) {
    return path.join(__dirname, '..', 'furniture_factory.db');
  }
};

// إنشاء اتصال قاعدة البيانات
const initializeDatabase = () => {
  return new Promise((resolve, reject) => {
    const dbPath = getDBPath();
    console.log('مسار قاعدة البيانات:', dbPath);
    
    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
        reject(err);
      } else {
        console.log('تم الاتصال بقاعدة البيانات بنجاح');
        createTables().then(resolve).catch(reject);
      }
    });
  });
};

// إنشاء الجداول
const createTables = () => {
  return new Promise((resolve, reject) => {
    const tables = [
      // جدول المواد (الأصلي - للتوافق مع النظام القديم)
      `CREATE TABLE IF NOT EXISTS materials (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        pricePerSqm REAL NOT NULL,
        category TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // جدول فئات المواد
      `CREATE TABLE IF NOT EXISTS material_categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        color TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // جدول المواد التفصيلية
      `CREATE TABLE IF NOT EXISTS detailed_materials (
        id TEXT PRIMARY KEY,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        categoryId TEXT NOT NULL,
        unit TEXT NOT NULL,
        purchasePrice REAL NOT NULL,
        salePrice REAL NOT NULL,
        availableQuantity REAL DEFAULT 0,
        minQuantity REAL DEFAULT 0,
        supplier TEXT,
        notes TEXT,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (categoryId) REFERENCES material_categories (id)
      )`,

      // جدول مواد المشاريع
      `CREATE TABLE IF NOT EXISTS project_materials (
        id TEXT PRIMARY KEY,
        projectId TEXT NOT NULL,
        materialId TEXT NOT NULL,
        requiredQuantity REAL NOT NULL,
        usedQuantity REAL DEFAULT 0,
        totalPurchaseCost REAL NOT NULL,
        totalSaleCost REAL NOT NULL,
        profit REAL NOT NULL,
        source TEXT CHECK(source IN ('external', 'stock')) DEFAULT 'external',
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (projectId) REFERENCES projects (id),
        FOREIGN KEY (materialId) REFERENCES detailed_materials (id)
      )`,
      
      // جدول العمال
      `CREATE TABLE IF NOT EXISTS workers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        pricePerSqm REAL NOT NULL,
        phone TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المصانع
      `CREATE TABLE IF NOT EXISTS factories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        pricePerSqm REAL NOT NULL,
        location TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المصممين
      `CREATE TABLE IF NOT EXISTS designers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        pricePerSqm REAL NOT NULL,
        phone TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول العملاء
      `CREATE TABLE IF NOT EXISTS customers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        totalProjects INTEGER DEFAULT 0,
        totalSpent REAL DEFAULT 0,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المشاريع
      `CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        customerName TEXT NOT NULL,
        customerPhone TEXT,
        area REAL NOT NULL,
        furnitureType TEXT NOT NULL,
        materialId TEXT NOT NULL,
        workerId TEXT NOT NULL,
        factoryId TEXT NOT NULL,
        designerId TEXT NOT NULL,
        totalCost REAL NOT NULL,
        materialCost REAL NOT NULL,
        workerCost REAL NOT NULL,
        factoryCost REAL NOT NULL,
        designerCost REAL NOT NULL,
        paidAmount REAL DEFAULT 0,
        remainingAmount REAL NOT NULL,
        status TEXT DEFAULT 'قيد التنفيذ',
        invoiceStatus TEXT DEFAULT 'مبدئية',
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        completedAt DATETIME,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول الفواتير
      `CREATE TABLE IF NOT EXISTS invoices (
        id TEXT PRIMARY KEY,
        projectId TEXT NOT NULL,
        customerName TEXT NOT NULL,
        customerPhone TEXT,
        totalAmount REAL NOT NULL,
        status TEXT DEFAULT 'مبدئية',
        type TEXT NOT NULL,
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول الموظفين
      `CREATE TABLE IF NOT EXISTS employees (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        position TEXT NOT NULL,
        baseSalary REAL NOT NULL,
        bonuses REAL DEFAULT 0,
        deductions REAL DEFAULT 0,
        totalSalary REAL NOT NULL,
        phone TEXT,
        hireDate DATE,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول معاملات الخزينة
      `CREATE TABLE IF NOT EXISTS cash_transactions (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        projectId TEXT,
        employeeId TEXT,
        date DATE NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    let completed = 0;
    const total = tables.length;

    tables.forEach((sql, index) => {
      db.run(sql, (err) => {
        if (err) {
          console.error(`خطأ في إنشاء الجدول ${index + 1}:`, err.message);
          reject(err);
          return;
        }

        completed++;
        if (completed === total) {
          console.log('تم إنشاء جميع الجداول بنجاح');
          // إضافة بيانات افتراضية للاختبار
          insertDefaultData();
          resolve();
        }
      });
    });
  });
};

// وظائف مساعدة لتنفيذ الاستعلامات
const runQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
};

const getQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

const allQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// إدراج البيانات الافتراضية للاختبار
const insertDefaultData = () => {
  // التحقق من وجود بيانات مسبقاً
  db.get('SELECT COUNT(*) as count FROM materials', (err, row) => {
    if (err || row.count > 0) return; // إذا كانت هناك بيانات، لا نضيف المزيد

    // إضافة مواد افتراضية
    const materials = [
      { id: '1', name: 'خشب MDF', description: 'خشب MDF عالي الجودة', pricePerSqm: 25, category: 'خشب' },
      { id: '2', name: 'خشب طبيعي', description: 'خشب طبيعي فاخر', pricePerSqm: 45, category: 'خشب' },
      { id: '3', name: 'معدن', description: 'معدن مقاوم للصدأ', pricePerSqm: 35, category: 'معدن' }
    ];

    materials.forEach(material => {
      db.run('INSERT OR IGNORE INTO materials (id, name, description, pricePerSqm, category) VALUES (?, ?, ?, ?, ?)',
        [material.id, material.name, material.description, material.pricePerSqm, material.category]);
    });

    // إضافة عمال افتراضيين
    const workers = [
      { id: '1', name: 'أحمد محمد', specialty: 'نجارة', pricePerSqm: 15, phone: '0912345678' },
      { id: '2', name: 'محمد علي', specialty: 'تجميع', pricePerSqm: 12, phone: '0923456789' }
    ];

    workers.forEach(worker => {
      db.run('INSERT OR IGNORE INTO workers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
        [worker.id, worker.name, worker.specialty, worker.pricePerSqm, worker.phone]);
    });

    // إضافة مصانع افتراضية
    const factories = [
      { id: '1', name: 'مصنع الأثاث الحديث', specialty: 'أثاث مكتبي', pricePerSqm: 20, location: 'طرابلس' },
      { id: '2', name: 'مصنع الخشب الفاخر', specialty: 'أثاث منزلي', pricePerSqm: 25, location: 'بنغازي' }
    ];

    factories.forEach(factory => {
      db.run('INSERT OR IGNORE INTO factories (id, name, specialty, pricePerSqm, location) VALUES (?, ?, ?, ?, ?)',
        [factory.id, factory.name, factory.specialty, factory.pricePerSqm, factory.location]);
    });

    // إضافة مصممين افتراضيين
    const designers = [
      { id: '1', name: 'سارة أحمد', specialty: 'تصميم داخلي', pricePerSqm: 10, phone: '0934567890' },
      { id: '2', name: 'خالد محمود', specialty: 'تصميم أثاث', pricePerSqm: 8, phone: '0945678901' }
    ];

    designers.forEach(designer => {
      db.run('INSERT OR IGNORE INTO designers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
        [designer.id, designer.name, designer.specialty, designer.pricePerSqm, designer.phone]);
    });

    console.log('تم إدراج البيانات الافتراضية للاختبار');
  });
};

// إغلاق قاعدة البيانات
const closeDatabase = () => {
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('خطأ في إغلاق قاعدة البيانات:', err.message);
      } else {
        console.log('تم إغلاق قاعدة البيانات بنجاح');
      }
    });
  }
};

module.exports = {
  initializeDatabase,
  runQuery,
  getQuery,
  allQuery,
  closeDatabase
};
