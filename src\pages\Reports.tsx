import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, FileText, BarChart3, TrendingUp, Calendar, Download, Eye, Edit2 } from "lucide-react";
import { Link } from "react-router-dom";
import Navbar from "@/components/Navbar";
import { formatLibyanDinar } from "@/utils/calculations";
import { getProjects, updateProject, Project } from "@/utils/dataManager";
import { useToast } from "@/hooks/use-toast";

const Reports = () => {
  const { toast } = useToast();
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedMonth, setSelectedMonth] = useState<string>("all");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      setLoading(true);
      const projectsData = await getProjects();
      setProjects(projectsData);
    } catch (error) {
      console.error('خطأ في تحميل المشاريع:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل المشاريع",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredProjects = projects.filter(project => {
    const statusMatch = selectedStatus === "all" || project.status === selectedStatus;
    const monthMatch = selectedMonth === "all" || 
      new Date(project.createdAt).getMonth() === parseInt(selectedMonth);
    return statusMatch && monthMatch;
  });

  const updateProjectStatus = async (projectId: string, newStatus: Project['status']) => {
    try {
      const success = await updateProject(projectId, { status: newStatus });
      if (success) {
        const updatedProjects = projects.map(project =>
          project.id === projectId ? { ...project, status: newStatus } : project
        );
        setProjects(updatedProjects);
        toast({
          title: "تم تحديث حالة المشروع",
          description: "تم حفظ التغييرات بنجاح"
        });
      } else {
        toast({
          title: "خطأ في التحديث",
          description: "فشل في تحديث حالة المشروع",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('خطأ في تحديث المشروع:', error);
      toast({
        title: "خطأ في التحديث",
        description: "حدث خطأ أثناء تحديث المشروع",
        variant: "destructive"
      });
    }
  };

  const generateDetailedReport = () => {
    const reportContent = `
تقرير مفصل - مصنع الأثاث
========================

تاريخ التقرير: ${new Date().toLocaleDateString('ar-LY')}
عدد المشاريع الإجمالي: ${projects.length}

الإحصائيات العامة:
- إجمالي الإيرادات: ${formatLibyanDinar(totalRevenue)}
- المبالغ المحصلة: ${formatLibyanDinar(totalPaid)}
- المبالغ المتبقية: ${formatLibyanDinar(totalRemaining)}

تفصيل المشاريع حسب الحالة:
- مكتمل: ${projects.filter(p => p.status === 'مكتمل').length}
- قيد التنفيذ: ${projects.filter(p => p.status === 'قيد التنفيذ').length}
- متأخر: ${projects.filter(p => p.status === 'متأخر').length}

تفاصيل المشاريع:
${filteredProjects.map(project => `
العميل: ${project.customerName}
التاريخ: ${new Date(project.createdAt).toLocaleDateString('ar-LY')}
المساحة: ${project.area} م²
التكلفة: ${formatLibyanDinar(project.totalCost)}
الحالة: ${project.status}
المادة: ${project.selectedMaterial?.name || 'غير محدد'}
العامل: ${project.selectedWorker.name}
المصنع: ${project.selectedFactory.name}
المصمم: ${project.selectedDesigner.name}
`).join('\n')}
`;

    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_مفصل_${new Date().toLocaleDateString('ar-LY').replace(/\//g, '-')}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const totalRevenue = filteredProjects.reduce((sum, project) => sum + project.totalCost, 0);
  const totalPaid = filteredProjects.reduce((sum, project) => sum + project.paidAmount, 0);
  const totalRemaining = filteredProjects.reduce((sum, project) => sum + project.remainingAmount, 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 flex items-center justify-center gap-3">
            <FileText className="h-8 w-8 text-blue-600" />
            التقارير والمشاريع
          </h1>
          <p className="text-gray-600 mt-2">عرض تقارير المشاريع والأرباح</p>
          <Button onClick={generateDetailedReport} className="bg-green-600 hover:bg-green-700 mt-4">
            <Download className="h-4 w-4 mr-2" />
            تحميل تقرير مفصل
          </Button>
        </header>

        {/* مرشحات */}
        <div className="grid md:grid-cols-2 gap-4 mb-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">تصفية حسب الحالة</label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="جميع الحالات" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="مكتمل">مكتمل</SelectItem>
                <SelectItem value="قيد التنفيذ">قيد التنفيذ</SelectItem>
                <SelectItem value="متأخر">متأخر</SelectItem>
                <SelectItem value="ملغي">ملغي</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">تصفية حسب الشهر</label>
            <Select value={selectedMonth} onValueChange={setSelectedMonth}>
              <SelectTrigger>
                <SelectValue placeholder="جميع الشهور" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الشهور</SelectItem>
                {Array.from({length: 12}, (_, i) => (
                  <SelectItem key={i} value={i.toString()}>
                    {new Date(2024, i).toLocaleDateString('ar-LY', { month: 'long' })}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* الإحصائيات */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-green-600">
                <TrendingUp className="h-5 w-5" />
                إجمالي الإيرادات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-green-700">{formatLibyanDinar(totalRevenue)}</p>
              <p className="text-sm text-gray-600 mt-2">من {filteredProjects.length} مشروع</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-blue-600">
                <BarChart3 className="h-5 w-5" />
                المبالغ المحصلة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-blue-700">{formatLibyanDinar(totalPaid)}</p>
              <p className="text-sm text-gray-600 mt-2">{((totalPaid/totalRevenue)*100).toFixed(1)}% من الإجمالي</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-orange-600">
                <Calendar className="h-5 w-5" />
                المبالغ المتبقية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-orange-700">{formatLibyanDinar(totalRemaining)}</p>
              <p className="text-sm text-gray-600 mt-2">{((totalRemaining/totalRevenue)*100).toFixed(1)}% من الإجمالي</p>
            </CardContent>
          </Card>
        </div>

        {/* جدول المشاريع */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
            <CardTitle>تفاصيل المشاريع ({filteredProjects.length})</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">جاري تحميل المشاريع...</p>
              </div>
            ) : filteredProjects.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full text-right">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="py-3 px-4 font-semibold text-gray-700">العميل</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">المساحة</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">التكلفة الإجمالية</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">المدفوع</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">المتبقي</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">التاريخ</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">الحالة</th>
                      <th className="py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredProjects.map((project) => (
                      <tr key={project.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium">{project.customerName}</p>
                            {project.customerPhone && (
                              <p className="text-sm text-gray-600">{project.customerPhone}</p>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">{project.area} م²</td>
                        <td className="py-3 px-4">{formatLibyanDinar(project.totalCost)}</td>
                        <td className="py-3 px-4 text-green-600">{formatLibyanDinar(project.paidAmount)}</td>
                        <td className="py-3 px-4 text-orange-600">{formatLibyanDinar(project.remainingAmount)}</td>
                        <td className="py-3 px-4">{new Date(project.createdAt).toLocaleDateString('ar-LY')}</td>
                        <td className="py-3 px-4">
                          <Select 
                            value={project.status} 
                            onValueChange={(value) => updateProjectStatus(project.id, value as Project['status'])}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="قيد التنفيذ">قيد التنفيذ</SelectItem>
                              <SelectItem value="مكتمل">مكتمل</SelectItem>
                              <SelectItem value="متأخر">متأخر</SelectItem>
                              <SelectItem value="ملغي">ملغي</SelectItem>
                            </SelectContent>
                          </Select>
                        </td>
                        <td className="py-3 px-4">
                          <Button size="sm" variant="outline" className="h-8 w-8 p-0" title="عرض التفاصيل">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد مشاريع</h3>
                <p className="text-gray-500 mb-4">ابدأ بإنشاء مشروع جديد من حاسبة التكلفة</p>
                <Link to="/">
                  <Button className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                    إنشاء مشروع جديد
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Reports;
