// تعريف أنواع Electron API
export interface ElectronAPI {
  // App info
  getVersion: () => Promise<string>;
  
  // File operations
  showSaveDialog: () => Promise<any>;
  showOpenDialog: () => Promise<any>;
  
  // Platform info
  platform: string;
  
  // Window controls
  minimize: () => Promise<void>;
  maximize: () => Promise<void>;
  close: () => Promise<void>;
  
  // Data export/import helpers
  exportData: (data: any) => Promise<void>;
  importData: () => Promise<any>;
  
  // Print functionality
  print: () => Promise<void>;
  
  // Notification
  showNotification: (title: string, body: string) => Promise<void>;
  
  // Database operations
  // المواد (النظام القديم)
  getMaterials: () => Promise<any[]>;
  addMaterial: (material: any) => Promise<string | null>;

  // فئات المواد التفصيلية
  getMaterialCategories: () => Promise<any[]>;
  addMaterialCategory: (category: any) => Promise<string | null>;

  // المواد التفصيلية
  getDetailedMaterials: () => Promise<any[]>;
  getDetailedMaterialsByCategory: (categoryId: string) => Promise<any[]>;
  addDetailedMaterial: (material: any) => Promise<string | null>;
  updateDetailedMaterial: (id: string, updates: any) => Promise<boolean>;
  deleteDetailedMaterial: (id: string) => Promise<boolean>;
  searchDetailedMaterials: (query: string) => Promise<any[]>;
  getDetailedMaterial: (id: string) => Promise<any | null>;
  updateMaterialStock: (materialId: string, quantityChange: number) => Promise<boolean>;

  // مواد المشاريع
  getProjectMaterials: (projectId: string) => Promise<any[]>;
  addProjectMaterial: (projectMaterial: any) => Promise<string | null>;
  updateProjectMaterial: (id: string, updates: any) => Promise<boolean>;
  deleteProjectMaterial: (id: string) => Promise<boolean>;

  // العمال
  getWorkers: () => Promise<any[]>;
  addWorker: (worker: any) => Promise<string | null>;

  // المصانع
  getFactories: () => Promise<any[]>;
  addFactory: (factory: any) => Promise<string | null>;

  // المصممين
  getDesigners: () => Promise<any[]>;
  addDesigner: (designer: any) => Promise<string | null>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
